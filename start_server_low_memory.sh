#!/bin/bash

# PPStructureV3 低内存模式启动脚本
# 适用于内存不足8GB的系统
# 使用方法: ./start_server_low_memory.sh [port]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认端口
DEFAULT_PORT=9999
PORT=${1:-$DEFAULT_PORT}

echo -e "${BLUE}=== PPStructureV3 低内存模式启动脚本 ===${NC}"
echo -e "${YELLOW}⚠️  注意：低内存模式将禁用部分功能以节省内存${NC}"
echo -e "${BLUE}服务端口: ${PORT}${NC}"
echo ""

# 检查系统内存
echo -e "${YELLOW}检查系统内存...${NC}"
if command -v free &> /dev/null; then
    TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.1f", $2/1024}')
    AVAIL_MEM=$(free -m | awk 'NR==2{printf "%.1f", $7/1024}')
    echo -e "${GREEN}总内存: ${TOTAL_MEM}GB, 可用内存: ${AVAIL_MEM}GB${NC}"
    
    if (( $(echo "$AVAIL_MEM < 4.0" | bc -l) )); then
        echo -e "${RED}⚠️  警告：可用内存不足4GB，建议释放更多内存${NC}"
    fi
elif command -v vm_stat &> /dev/null; then
    # macOS
    echo -e "${GREEN}macOS系统检测到${NC}"
fi

# 检查Python版本
echo -e "${YELLOW}检查Python环境...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}错误: 未找到Python3，请先安装Python3${NC}"
    exit 1
fi

PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
echo -e "${GREEN}Python版本: ${PYTHON_VERSION}${NC}"

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}创建虚拟环境...${NC}"
    python3 -m venv venv
    echo -e "${GREEN}虚拟环境创建完成${NC}"
fi

# 创建必要目录
echo -e "${YELLOW}创建必要目录...${NC}"
mkdir -p logs uploads

# 激活虚拟环境
echo -e "${YELLOW}激活虚拟环境...${NC}"
source venv/bin/activate

# 验证关键依赖
echo -e "${YELLOW}验证关键依赖...${NC}"
python3 -c "import flask; print('Flask: OK')" 2>/dev/null || echo -e "${RED}Flask未安装，请先运行: pip install -r requirements.txt${NC}"
python3 -c "from paddleocr import PPStructureV3; print('PP-StructureV3: OK')" 2>/dev/null || echo -e "${YELLOW}PP-StructureV3未安装或有问题，请先运行: pip install -r requirements.txt${NC}"

# 设置环境变量
export FLASK_APP=pp_structure_v3_server.py
export FLASK_ENV=production  # 使用生产模式减少内存
export PADDLEX_AUTO_UPGRADE=False

# 严格的内存优化环境变量
echo -e "${YELLOW}设置内存优化参数...${NC}"
export OMP_NUM_THREADS=1  # 限制OpenMP线程数为1
export MKL_NUM_THREADS=1  # 限制MKL线程数为1
export NUMEXPR_NUM_THREADS=1  # 限制NumExpr线程数为1
export OPENBLAS_NUM_THREADS=1  # 限制OpenBLAS线程数为1

# PaddlePaddle严格内存优化
export FLAGS_fraction_of_gpu_memory_to_use=0.05  # 极限制GPU内存使用
export FLAGS_allocator_strategy=naive_best_fit  # 使用内存优化分配策略
export FLAGS_eager_delete_tensor_gb=0.0  # 立即释放张量内存
export FLAGS_memory_fraction_of_eager_deletion=1.0  # 积极释放内存

# Python内存优化
export PYTHONHASHSEED=0  # 固定哈希种子减少内存碎片
export MALLOC_TRIM_THRESHOLD_=100000  # 更积极的内存回收

# 启动服务
echo ""
echo -e "${GREEN}=== 启动PPStructureV3 低内存模式服务 ===${NC}"
echo -e "${YELLOW}功能限制：${NC}"
echo -e "${YELLOW}  - 禁用印章识别${NC}"
echo -e "${YELLOW}  - 禁用图表识别${NC}"
echo -e "${YELLOW}  - 禁用公式识别${NC}"
echo -e "${YELLOW}  - 批处理大小设为1${NC}"
echo -e "${YELLOW}  - 限制图像处理尺寸${NC}"
echo ""
echo -e "${GREEN}服务地址: http://localhost:${PORT}${NC}"
echo -e "${GREEN}API文档: http://localhost:${PORT}${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
echo ""

# 启动Flask应用
python3 pp_structure_v3_server.py --port ${PORT} --host 0.0.0.0
