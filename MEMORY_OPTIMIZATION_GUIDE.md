# PP-StructureV3 内存优化指南

## 🚨 内存占用过高问题解决方案

### 问题描述
PP-StructureV3模型较大，在内存不足的系统上可能出现：
- 模型加载失败
- PDF处理卡死
- 系统内存耗尽
- 服务响应缓慢

### 📊 内存需求评估

| 系统内存 | 推荐方案 | 功能限制 |
|---------|---------|---------|
| < 4GB | 极低内存模式 | 禁用印章、公式、图表识别 |
| 4-6GB | 低内存模式 | 禁用部分高级功能 |
| 6-8GB | 标准模式 | 启用大部分功能 |
| > 8GB | 完整模式 | 启用所有功能 |

## 🛠️ 解决方案

### 方案1：使用极低内存模式（推荐）

```bash
# 启动极低内存模式服务
./start_low_memory.sh

# 或者直接运行
python3 low_memory_server.py
```

**特点：**
- 只保留核心功能：版面分析、OCR、表格识别
- 禁用：印章识别、公式识别、图表识别
- 内存占用减少60-70%
- 处理速度更快

### 方案2：使用内存优化的标准模式

```bash
# 启动内存优化模式
./start_server_low_memory.sh
```

**特点：**
- 自动根据系统内存调整配置
- 动态启用/禁用功能
- 内存监控和垃圾回收

### 方案3：手动优化现有服务

修改 `pp_structure_v3_server.py` 中的模型配置：

```python
# 在模型初始化时使用以下配置
self.model = PPStructureV3(
    use_doc_orientation_classify=False,
    use_doc_unwarping=False,
    use_textline_orientation=False,
    use_seal_recognition=False,      # 禁用印章识别
    use_chart_recognition=False,     # 禁用图表识别
    use_formula_recognition=False,   # 禁用公式识别
    text_det_limit_side_len=640,     # 限制图像尺寸
)
```

## 🔧 系统级优化

### 1. 环境变量优化

```bash
# 限制线程数
export OMP_NUM_THREADS=1
export MKL_NUM_THREADS=1
export NUMEXPR_NUM_THREADS=1
export OPENBLAS_NUM_THREADS=1

# PaddlePaddle内存优化
export FLAGS_fraction_of_gpu_memory_to_use=0.01
export FLAGS_allocator_strategy=naive_best_fit
export FLAGS_eager_delete_tensor_gb=0.0
```

### 2. 系统内存释放

```bash
# Linux系统
sudo sync
sudo echo 3 > /proc/sys/vm/drop_caches

# macOS系统
sudo purge
```

### 3. 交换空间配置

```bash
# 检查交换空间
free -h

# 创建交换文件（如果没有）
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

## 📈 内存监控

### 实时监控内存使用

```bash
# 监控总体内存
watch -n 1 free -h

# 监控Python进程
watch -n 1 "ps aux | grep python | grep -v grep"

# 使用htop（如果安装）
htop
```

### 服务内置监控

访问 `http://localhost:9999/health` 查看：
- 模型状态
- 内存使用情况
- 系统资源状态

## 🎯 针对性优化建议

### PDF文件处理优化

1. **文件大小限制**
   ```python
   # 限制PDF文件大小（如20MB）
   MAX_PDF_SIZE = 20 * 1024 * 1024  # 20MB
   ```

2. **分页处理**
   ```python
   # 对大PDF进行分页处理
   if file_size > 10:  # 10MB以上分页处理
       # 实现分页逻辑
   ```

3. **图像尺寸限制**
   ```python
   # 限制处理图像的最大尺寸
   text_det_limit_side_len=640  # 从默认960降到640
   ```

### 批处理优化

```python
# 减小批处理大小
text_recognition_batch_size=1
table_recognition_batch_size=1
```

## 🚀 性能对比

| 模式 | 内存占用 | 处理速度 | 功能完整性 |
|------|---------|---------|-----------|
| 完整模式 | 6-8GB | 慢 | 100% |
| 标准模式 | 4-6GB | 中等 | 80% |
| 低内存模式 | 2-4GB | 快 | 60% |
| 极低内存模式 | 1-2GB | 很快 | 40% |

## 🔍 故障排除

### 常见问题

1. **模型加载失败**
   ```
   解决：使用极低内存模式
   命令：./start_low_memory.sh
   ```

2. **PDF处理卡死**
   ```
   解决：限制PDF文件大小，启用超时机制
   ```

3. **内存不足错误**
   ```
   解决：关闭其他应用，增加交换空间
   ```

4. **处理速度慢**
   ```
   解决：禁用不必要的功能，减小图像尺寸
   ```

### 日志分析

查看日志文件了解内存使用情况：
```bash
tail -f logs/pp_structure_v3_*.log | grep -i memory
```

## 📝 最佳实践

1. **启动前检查**
   - 确保至少有2GB可用内存
   - 关闭不必要的应用程序
   - 检查交换空间配置

2. **运行时监控**
   - 定期检查内存使用率
   - 监控处理时间
   - 观察错误日志

3. **文件处理策略**
   - 优先处理小文件
   - 大文件分批处理
   - 设置合理的超时时间

4. **系统维护**
   - 定期重启服务释放内存
   - 清理临时文件
   - 更新系统和依赖

## 🎉 总结

通过以上优化措施，可以显著降低PP-StructureV3的内存占用：

- **立即解决方案**：使用 `./start_low_memory.sh` 启动极低内存模式
- **长期优化**：根据系统配置选择合适的运行模式
- **监控维护**：定期检查内存使用情况，及时调整配置

选择适合您系统的方案，即可解决内存占用过高的问题！
