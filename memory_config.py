#!/usr/bin/env python3
"""
PP-StructureV3 内存优化配置
用于减少内存占用和提高性能
"""

import os
import gc
import psutil
import logging

logger = logging.getLogger(__name__)

class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.initial_memory = None
        self.peak_memory = 0
        
    def setup_environment(self):
        """设置内存优化环境变量"""
        # 线程数限制
        os.environ['OMP_NUM_THREADS'] = '1'
        os.environ['MKL_NUM_THREADS'] = '1'
        os.environ['NUMEXPR_NUM_THREADS'] = '1'
        os.environ['OPENBLAS_NUM_THREADS'] = '1'
        
        # PaddlePaddle内存优化
        os.environ['FLAGS_fraction_of_gpu_memory_to_use'] = '0.05'
        os.environ['FLAGS_allocator_strategy'] = 'naive_best_fit'
        os.environ['FLAGS_eager_delete_tensor_gb'] = '0.0'
        os.environ['FLAGS_memory_fraction_of_eager_deletion'] = '1.0'
        
        # Python内存优化
        os.environ['PYTHONHASHSEED'] = '0'
        os.environ['MALLOC_TRIM_THRESHOLD_'] = '100000'
        
        logger.info("内存优化环境变量已设置")
    
    def get_memory_info(self):
        """获取当前内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        system_memory = psutil.virtual_memory()
        
        return {
            'process_rss': memory_info.rss / (1024 * 1024),  # MB
            'process_vms': memory_info.vms / (1024 * 1024),  # MB
            'system_total': system_memory.total / (1024 * 1024 * 1024),  # GB
            'system_available': system_memory.available / (1024 * 1024 * 1024),  # GB
            'system_percent': system_memory.percent
        }
    
    def log_memory_usage(self, stage=""):
        """记录内存使用情况"""
        memory_info = self.get_memory_info()
        
        if self.initial_memory is None:
            self.initial_memory = memory_info['process_rss']
        
        current_memory = memory_info['process_rss']
        if current_memory > self.peak_memory:
            self.peak_memory = current_memory
        
        logger.info(f"内存使用 {stage}: "
                   f"进程RSS={current_memory:.1f}MB, "
                   f"系统可用={memory_info['system_available']:.1f}GB "
                   f"({100-memory_info['system_percent']:.1f}%)")
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        collected = gc.collect()
        logger.debug(f"垃圾回收释放了 {collected} 个对象")
        return collected
    
    def check_memory_pressure(self):
        """检查内存压力"""
        memory_info = self.get_memory_info()
        
        # 如果系统内存使用超过85%，发出警告
        if memory_info['system_percent'] > 85:
            logger.warning(f"系统内存使用率过高: {memory_info['system_percent']:.1f}%")
            return True
        
        # 如果进程内存超过2GB，发出警告
        if memory_info['process_rss'] > 2048:
            logger.warning(f"进程内存使用过高: {memory_info['process_rss']:.1f}MB")
            return True
        
        return False
    
    def optimize_for_pdf_processing(self):
        """PDF处理前的内存优化"""
        self.force_garbage_collection()
        self.log_memory_usage("PDF处理前")
        
        # 检查内存压力
        if self.check_memory_pressure():
            logger.warning("内存压力较大，建议：")
            logger.warning("1. 关闭其他应用程序")
            logger.warning("2. 处理较小的PDF文件")
            logger.warning("3. 重启服务释放内存")

# 全局内存优化器实例
memory_optimizer = MemoryOptimizer()

def get_optimal_ppstructure_config():
    """获取内存优化的PP-StructureV3配置"""
    memory_info = memory_optimizer.get_memory_info()
    
    # 根据可用内存调整配置
    if memory_info['system_available'] < 4.0:  # 少于4GB可用内存
        config = {
            'use_doc_orientation_classify': False,
            'use_doc_unwarping': False,
            'use_textline_orientation': False,
            'use_seal_recognition': False,
            'use_chart_recognition': False,
            'use_formula_recognition': False,
            'use_table_recognition': True,  # 保留表格识别
            'text_recognition_batch_size': 1,
            'table_recognition_batch_size': 1,
            'use_gpu': False,
            'show_log': False,
            'text_det_limit_side_len': 640,  # 更小的图像尺寸
        }
        logger.info("使用极低内存配置")
    elif memory_info['system_available'] < 6.0:  # 少于6GB可用内存
        config = {
            'use_doc_orientation_classify': False,
            'use_doc_unwarping': False,
            'use_textline_orientation': False,
            'use_seal_recognition': False,
            'use_chart_recognition': False,
            'use_formula_recognition': False,
            'use_table_recognition': True,
            'text_recognition_batch_size': 2,
            'table_recognition_batch_size': 1,
            'use_gpu': False,
            'show_log': False,
            'text_det_limit_side_len': 960,
        }
        logger.info("使用低内存配置")
    else:  # 6GB以上可用内存
        config = {
            'use_doc_orientation_classify': False,
            'use_doc_unwarping': False,
            'use_textline_orientation': False,
            'use_seal_recognition': False,
            'use_chart_recognition': True,  # 启用图表识别
            'use_formula_recognition': True,  # 启用公式识别
            'use_table_recognition': True,
            'text_recognition_batch_size': 4,
            'table_recognition_batch_size': 2,
            'use_gpu': False,
            'show_log': False,
            'text_det_limit_side_len': 1280,
        }
        logger.info("使用标准内存配置")
    
    return config

def setup_memory_optimization():
    """设置内存优化"""
    memory_optimizer.setup_environment()
    memory_optimizer.log_memory_usage("初始化")
    return memory_optimizer

if __name__ == "__main__":
    # 测试内存优化器
    optimizer = setup_memory_optimization()
    config = get_optimal_ppstructure_config()
    print("推荐配置:", config)
