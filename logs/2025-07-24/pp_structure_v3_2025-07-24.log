2025-07-24 09:36:22 - pp_structure_v3 - INFO - main:354 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 09:36:22 - pp_structure_v3 - INFO - main:355 - 服务地址: http://0.0.0.0:9999
2025-07-24 09:36:22 - pp_structure_v3 - INFO - main:356 - API文档:
2025-07-24 09:36:22 - pp_structure_v3 - INFO - main:357 -   GET  /              - 服务信息
2025-07-24 09:36:22 - pp_structure_v3 - INFO - main:358 -   GET  /health        - 健康检查
2025-07-24 09:36:22 - pp_structure_v3 - INFO - main:359 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 09:36:22 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-24 09:36:36 - pp_structure_v3 - INFO - init_model:83 - ✅ PP-StructureV3模型初始化成功
2025-07-24 09:36:37 - pp_structure_v3 - INFO - main:354 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 09:36:37 - pp_structure_v3 - INFO - main:355 - 服务地址: http://0.0.0.0:9999
2025-07-24 09:36:37 - pp_structure_v3 - INFO - main:356 - API文档:
2025-07-24 09:36:37 - pp_structure_v3 - INFO - main:357 -   GET  /              - 服务信息
2025-07-24 09:36:37 - pp_structure_v3 - INFO - main:358 -   GET  /health        - 健康检查
2025-07-24 09:36:37 - pp_structure_v3 - INFO - main:359 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 09:36:37 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-24 09:36:51 - pp_structure_v3 - INFO - init_model:83 - ✅ PP-StructureV3模型初始化成功
2025-07-24 09:36:54 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-24 09:36:54 - pp_structure_v3 - DEBUG - log_request_info:145 - Headers: {'Accept': 'application/json', 'Content-Type': 'multipart/form-data; boundary=667001c3-fcbe-4757-ae1b-8aecd2a532c2', 'Content-Length': '459203', 'Host': 'localhost:9999', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip', 'User-Agent': 'okhttp/4.9.3'}
2025-07-24 09:36:54 - pp_structure_v3 - INFO - structure_file:297 - 处理上传文件: document.pdf
2025-07-24 09:36:54 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-24 09:36:54 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpas_0l16_.pdf
2025-07-24 09:36:54 - pp_structure_v3 - DEBUG - predict:110 - 文件扩展名: .pdf
2025-07-24 09:36:54 - pp_structure_v3 - INFO - predict:115 - 处理PDF文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpas_0l16_.pdf
2025-07-24 09:50:16 - pp_structure_v3 - INFO - main:411 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 09:50:16 - pp_structure_v3 - INFO - main:412 - 服务地址: http://0.0.0.0:9999
2025-07-24 09:50:16 - pp_structure_v3 - INFO - main:413 - API文档:
2025-07-24 09:50:16 - pp_structure_v3 - INFO - main:414 -   GET  /              - 服务信息
2025-07-24 09:50:16 - pp_structure_v3 - INFO - main:415 -   GET  /health        - 健康检查
2025-07-24 09:50:16 - pp_structure_v3 - INFO - main:416 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 09:50:16 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-24 09:50:16 - pp_structure_v3 - INFO - init_model:77 - 系统内存: 总计 32.0GB, 可用 20.6GB
2025-07-24 09:50:17 - pp_structure_v3 - ERROR - init_model:104 - PP-StructureV3初始化失败: Unknown argument: show_log
2025-07-24 09:50:17 - pp_structure_v3 - ERROR - log_error:158 - Error: Unknown argument: show_log
2025-07-24 09:50:17 - pp_structure_v3 - ERROR - log_error:160 - Context: PP-StructureV3初始化
2025-07-24 09:50:17 - pp_structure_v3 - ERROR - log_error:161 - Exception details:
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/PPStructureV3Server 2/pp_structure_v3_server.py", line 86, in init_model
    self.model = PPStructureV3(
                 ^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/pp_structurev3.py", line 98, in __init__
    super().__init__(**kwargs)
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/base.py", line 62, in __init__
    self._common_args = parse_common_args(
                        ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_common_args.py", line 43, in parse_common_args
    raise ValueError(f"Unknown argument: {name}")
ValueError: Unknown argument: show_log
2025-07-24 09:50:17 - pp_structure_v3 - WARNING - main:420 - 模型初始化失败，将在首次请求时重试
2025-07-24 09:50:17 - pp_structure_v3 - INFO - main:411 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 09:50:17 - pp_structure_v3 - INFO - main:412 - 服务地址: http://0.0.0.0:9999
2025-07-24 09:50:17 - pp_structure_v3 - INFO - main:413 - API文档:
2025-07-24 09:50:17 - pp_structure_v3 - INFO - main:414 -   GET  /              - 服务信息
2025-07-24 09:50:17 - pp_structure_v3 - INFO - main:415 -   GET  /health        - 健康检查
2025-07-24 09:50:17 - pp_structure_v3 - INFO - main:416 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 09:50:17 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-24 09:50:17 - pp_structure_v3 - INFO - init_model:77 - 系统内存: 总计 32.0GB, 可用 20.5GB
2025-07-24 09:50:18 - pp_structure_v3 - ERROR - init_model:104 - PP-StructureV3初始化失败: Unknown argument: use_gpu
2025-07-24 09:50:18 - pp_structure_v3 - ERROR - log_error:158 - Error: Unknown argument: use_gpu
2025-07-24 09:50:18 - pp_structure_v3 - ERROR - log_error:160 - Context: PP-StructureV3初始化
2025-07-24 09:50:18 - pp_structure_v3 - ERROR - log_error:161 - Exception details:
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/PPStructureV3Server 2/pp_structure_v3_server.py", line 86, in init_model
    self.model = PPStructureV3(
                 ^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/pp_structurev3.py", line 98, in __init__
    super().__init__(**kwargs)
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/base.py", line 62, in __init__
    self._common_args = parse_common_args(
                        ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_common_args.py", line 43, in parse_common_args
    raise ValueError(f"Unknown argument: {name}")
ValueError: Unknown argument: use_gpu
2025-07-24 09:50:18 - pp_structure_v3 - WARNING - main:420 - 模型初始化失败，将在首次请求时重试
2025-07-24 09:50:33 - pp_structure_v3 - INFO - main:354 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 09:50:33 - pp_structure_v3 - INFO - main:355 - 服务地址: http://0.0.0.0:9999
2025-07-24 09:50:33 - pp_structure_v3 - INFO - main:356 - API文档:
2025-07-24 09:50:33 - pp_structure_v3 - INFO - main:357 -   GET  /              - 服务信息
2025-07-24 09:50:33 - pp_structure_v3 - INFO - main:358 -   GET  /health        - 健康检查
2025-07-24 09:50:33 - pp_structure_v3 - INFO - main:359 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 09:50:33 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-24 09:50:46 - pp_structure_v3 - INFO - init_model:83 - ✅ PP-StructureV3模型初始化成功
2025-07-24 09:52:26 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-24 09:52:26 - pp_structure_v3 - DEBUG - log_request_info:145 - Headers: {'Accept': 'application/json', 'Content-Type': 'multipart/form-data; boundary=e89af26e-ab0c-48fe-b5b2-3b90f9f21f11', 'Content-Length': '459074', 'Host': 'localhost:9999', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip', 'User-Agent': 'okhttp/4.9.3'}
2025-07-24 09:52:26 - pp_structure_v3 - INFO - structure_file:297 - 处理上传文件: document.pdf
2025-07-24 09:52:26 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-24 09:52:26 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpisj_gqmr.pdf
2025-07-24 09:52:26 - pp_structure_v3 - DEBUG - predict:110 - 文件扩展名: .pdf
2025-07-24 09:52:26 - pp_structure_v3 - INFO - predict:115 - 处理PDF文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpisj_gqmr.pdf
2025-07-24 09:54:24 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://127.0.0.1:9999/api/v1/structure/file
2025-07-24 09:54:24 - pp_structure_v3 - DEBUG - log_request_info:145 - Headers: {'User-Agent': 'Apifox/1.0.0 (https://apifox.com)', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJqb0hBOEVMZWYxNnFmb0xiZGI1bGVocUlhMUlUVFVmTCJ9.9W5bRGR9mYnZdX7NtubmP89rj1pwqA9rEmV3diT4sOQ', 'Accept': '*/*', 'Host': '127.0.0.1:9999', 'Accept-Encoding': 'gzip, deflate, br', 'Connection': 'keep-alive', 'Content-Length': '0'}
2025-07-24 09:54:24 - pp_structure_v3 - WARNING - structure_file:270 - 请求中没有文件
2025-07-24 09:54:24 - pp_structure_v3 - INFO - log_response_info:152 - Response: Unknown
2025-07-24 09:54:24 - pp_structure_v3 - INFO - log_response_info:154 - Duration: 0.002s
2025-07-24 09:54:41 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://127.0.0.1:9999/api/v1/structure/file
2025-07-24 09:54:41 - pp_structure_v3 - DEBUG - log_request_info:145 - Headers: {'User-Agent': 'Apifox/1.0.0 (https://apifox.com)', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJqb0hBOEVMZWYxNnFmb0xiZGI1bGVocUlhMUlUVFVmTCJ9.9W5bRGR9mYnZdX7NtubmP89rj1pwqA9rEmV3diT4sOQ', 'Accept': '*/*', 'Host': '127.0.0.1:9999', 'Accept-Encoding': 'gzip, deflate, br', 'Connection': 'keep-alive', 'Content-Type': 'multipart/form-data; boundary=--------------------------481245268669065646310881', 'Content-Length': '803124'}
2025-07-24 09:54:41 - pp_structure_v3 - INFO - structure_file:297 - 处理上传文件: **********-1105422x.png
2025-07-24 09:54:41 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-24 09:54:41 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpzojp4x7z.png
2025-07-24 09:54:41 - pp_structure_v3 - DEBUG - predict:110 - 文件扩展名: .png
2025-07-24 09:54:41 - pp_structure_v3 - INFO - predict:117 - 处理图像文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpzojp4x7z.png
2025-07-24 09:55:01 - pp_structure_v3 - DEBUG - predict:120 - PP-StructureV3预测成功，返回结果类型: <class 'list'>
2025-07-24 09:55:01 - pp_structure_v3 - DEBUG - predict:146 - 处理 1 个结果
2025-07-24 09:55:01 - pp_structure_v3 - DEBUG - predict:151 - 处理第 1 个结果，类型: <class 'paddlex.inference.pipelines.layout_parsing.result_v2.LayoutParsingResultV2'>
2025-07-24 09:55:01 - pp_structure_v3 - INFO - predict:203 - PP-StructureV3成功处理 1 个图像
2025-07-24 09:55:01 - pp_structure_v3 - INFO - structure_file:309 - 文件 **********-1105422x.png 解析完成
2025-07-24 09:55:01 - pp_structure_v3 - DEBUG - structure_file:330 - 清理临时文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpzojp4x7z.png
2025-07-24 09:55:01 - pp_structure_v3 - INFO - log_response_info:152 - Response: 200
2025-07-24 09:55:01 - pp_structure_v3 - INFO - log_response_info:154 - Duration: 20.008s
2025-07-24 09:55:15 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://127.0.0.1:9999/api/v1/structure/file
2025-07-24 09:55:15 - pp_structure_v3 - DEBUG - log_request_info:145 - Headers: {'User-Agent': 'Apifox/1.0.0 (https://apifox.com)', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJqb0hBOEVMZWYxNnFmb0xiZGI1bGVocUlhMUlUVFVmTCJ9.9W5bRGR9mYnZdX7NtubmP89rj1pwqA9rEmV3diT4sOQ', 'Accept': '*/*', 'Host': '127.0.0.1:9999', 'Accept-Encoding': 'gzip, deflate, br', 'Connection': 'keep-alive', 'Content-Type': 'multipart/form-data; boundary=--------------------------095396336082379878333187', 'Content-Length': '459119'}
2025-07-24 09:55:15 - pp_structure_v3 - INFO - structure_file:297 - 处理上传文件: 1.pdf
2025-07-24 09:55:15 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-24 09:55:15 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmp5680f4b0.pdf
2025-07-24 09:55:15 - pp_structure_v3 - DEBUG - predict:110 - 文件扩展名: .pdf
2025-07-24 09:55:15 - pp_structure_v3 - INFO - predict:115 - 处理PDF文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmp5680f4b0.pdf
2025-07-24 09:55:39 - pp_structure_v3 - DEBUG - predict:120 - PP-StructureV3预测成功，返回结果类型: <class 'list'>
2025-07-24 09:55:39 - pp_structure_v3 - DEBUG - predict:146 - 处理 1 个结果
2025-07-24 09:55:39 - pp_structure_v3 - DEBUG - predict:151 - 处理第 1 个结果，类型: <class 'paddlex.inference.pipelines.layout_parsing.result_v2.LayoutParsingResultV2'>
2025-07-24 09:55:39 - pp_structure_v3 - INFO - predict:203 - PP-StructureV3成功处理 1 个图像
2025-07-24 09:55:39 - pp_structure_v3 - INFO - structure_file:309 - 文件 1.pdf 解析完成
2025-07-24 09:55:39 - pp_structure_v3 - DEBUG - structure_file:330 - 清理临时文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmp5680f4b0.pdf
2025-07-24 09:55:39 - pp_structure_v3 - INFO - log_response_info:152 - Response: 200
2025-07-24 09:55:39 - pp_structure_v3 - INFO - log_response_info:154 - Duration: 24.201s
2025-07-24 09:56:02 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-24 09:56:02 - pp_structure_v3 - DEBUG - log_request_info:145 - Headers: {'Accept': 'application/json', 'Content-Type': 'multipart/form-data; boundary=90f1d29e-5af4-4425-ba41-c5df8fab2811', 'Content-Length': '459074', 'Host': 'localhost:9999', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip', 'User-Agent': 'okhttp/4.9.3'}
2025-07-24 09:56:02 - pp_structure_v3 - INFO - structure_file:297 - 处理上传文件: document.pdf
2025-07-24 09:56:02 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-24 09:56:02 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpq36mi5c4.pdf
2025-07-24 09:56:02 - pp_structure_v3 - DEBUG - predict:110 - 文件扩展名: .pdf
2025-07-24 09:56:02 - pp_structure_v3 - INFO - predict:115 - 处理PDF文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpq36mi5c4.pdf
2025-07-24 09:57:02 - pp_structure_v3 - DEBUG - predict:120 - PP-StructureV3预测成功，返回结果类型: <class 'list'>
2025-07-24 09:57:02 - pp_structure_v3 - DEBUG - predict:146 - 处理 1 个结果
2025-07-24 09:57:02 - pp_structure_v3 - DEBUG - predict:151 - 处理第 1 个结果，类型: <class 'paddlex.inference.pipelines.layout_parsing.result_v2.LayoutParsingResultV2'>
2025-07-24 09:57:02 - pp_structure_v3 - INFO - predict:203 - PP-StructureV3成功处理 1 个图像
2025-07-24 09:57:02 - pp_structure_v3 - INFO - structure_file:309 - 文件 document.pdf 解析完成
2025-07-24 09:57:02 - pp_structure_v3 - DEBUG - structure_file:330 - 清理临时文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpq36mi5c4.pdf
2025-07-24 09:57:02 - pp_structure_v3 - INFO - log_response_info:152 - Response: 200
2025-07-24 09:57:02 - pp_structure_v3 - INFO - log_response_info:154 - Duration: 60.846s
2025-07-24 09:57:40 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-24 09:57:40 - pp_structure_v3 - DEBUG - log_request_info:145 - Headers: {'Accept': 'application/json', 'Content-Type': 'multipart/form-data; boundary=d86ad215-d3b8-4f5a-8c9a-1968d21281c6', 'Content-Length': '459074', 'Host': 'localhost:9999', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip', 'User-Agent': 'okhttp/4.9.3'}
2025-07-24 09:57:40 - pp_structure_v3 - INFO - structure_file:297 - 处理上传文件: document.pdf
2025-07-24 09:57:40 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-24 09:57:40 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpkf9onwr_.pdf
2025-07-24 09:57:40 - pp_structure_v3 - DEBUG - predict:110 - 文件扩展名: .pdf
2025-07-24 09:57:40 - pp_structure_v3 - INFO - predict:115 - 处理PDF文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpkf9onwr_.pdf
2025-07-24 09:58:04 - pp_structure_v3 - DEBUG - predict:120 - PP-StructureV3预测成功，返回结果类型: <class 'list'>
2025-07-24 09:58:04 - pp_structure_v3 - DEBUG - predict:146 - 处理 1 个结果
2025-07-24 09:58:04 - pp_structure_v3 - DEBUG - predict:151 - 处理第 1 个结果，类型: <class 'paddlex.inference.pipelines.layout_parsing.result_v2.LayoutParsingResultV2'>
2025-07-24 09:58:04 - pp_structure_v3 - INFO - predict:203 - PP-StructureV3成功处理 1 个图像
2025-07-24 09:58:04 - pp_structure_v3 - INFO - structure_file:309 - 文件 document.pdf 解析完成
2025-07-24 09:58:04 - pp_structure_v3 - DEBUG - structure_file:330 - 清理临时文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpkf9onwr_.pdf
2025-07-24 09:58:04 - pp_structure_v3 - INFO - log_response_info:152 - Response: 200
2025-07-24 09:58:04 - pp_structure_v3 - INFO - log_response_info:154 - Duration: 23.207s
2025-07-24 09:58:32 - pp_structure_v3 - INFO - main:354 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 09:58:32 - pp_structure_v3 - INFO - main:355 - 服务地址: http://0.0.0.0:9999
2025-07-24 09:58:32 - pp_structure_v3 - INFO - main:356 - API文档:
2025-07-24 09:58:32 - pp_structure_v3 - INFO - main:357 -   GET  /              - 服务信息
2025-07-24 09:58:32 - pp_structure_v3 - INFO - main:358 -   GET  /health        - 健康检查
2025-07-24 09:58:32 - pp_structure_v3 - INFO - main:359 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 09:58:32 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-24 09:58:45 - pp_structure_v3 - INFO - init_model:83 - ✅ PP-StructureV3模型初始化成功
2025-07-24 09:58:46 - pp_structure_v3 - INFO - main:354 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 09:58:46 - pp_structure_v3 - INFO - main:355 - 服务地址: http://0.0.0.0:9999
2025-07-24 09:58:46 - pp_structure_v3 - INFO - main:356 - API文档:
2025-07-24 09:58:46 - pp_structure_v3 - INFO - main:357 -   GET  /              - 服务信息
2025-07-24 09:58:46 - pp_structure_v3 - INFO - main:358 -   GET  /health        - 健康检查
2025-07-24 09:58:46 - pp_structure_v3 - INFO - main:359 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 09:58:46 - pp_structure_v3 - INFO - init_model:72 - 开始初始化PP-StructureV3模型...
2025-07-24 09:58:58 - pp_structure_v3 - INFO - init_model:83 - ✅ PP-StructureV3模型初始化成功
2025-07-24 09:59:05 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-24 09:59:05 - pp_structure_v3 - DEBUG - log_request_info:145 - Headers: {'Accept': 'application/json', 'Content-Type': 'multipart/form-data; boundary=1f49f73b-1bce-435b-92b1-ac5d967ad890', 'Content-Length': '459074', 'Host': 'localhost:9999', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip', 'User-Agent': 'okhttp/4.9.3'}
2025-07-24 09:59:05 - pp_structure_v3 - INFO - structure_file:297 - 处理上传文件: document.pdf
2025-07-24 09:59:05 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-24 09:59:05 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmphzmpdgln.pdf
2025-07-24 09:59:05 - pp_structure_v3 - DEBUG - predict:110 - 文件扩展名: .pdf
2025-07-24 09:59:05 - pp_structure_v3 - INFO - predict:115 - 处理PDF文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmphzmpdgln.pdf
2025-07-24 10:00:35 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-24 10:00:35 - pp_structure_v3 - DEBUG - log_request_info:145 - Headers: {'Accept': 'application/json', 'Content-Type': 'multipart/form-data; boundary=1555dea1-f03b-4396-b555-a655460233f6', 'Content-Length': '459074', 'Host': 'localhost:9999', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip', 'User-Agent': 'okhttp/4.9.3'}
2025-07-24 10:00:35 - pp_structure_v3 - INFO - structure_file:297 - 处理上传文件: document.pdf
2025-07-24 10:00:35 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-24 10:00:35 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpf3k_gqsx.pdf
2025-07-24 10:00:35 - pp_structure_v3 - DEBUG - predict:110 - 文件扩展名: .pdf
2025-07-24 10:00:35 - pp_structure_v3 - INFO - predict:115 - 处理PDF文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmpf3k_gqsx.pdf
2025-07-24 10:01:11 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://localhost:9999/api/v1/structure/file
2025-07-24 10:01:11 - pp_structure_v3 - DEBUG - log_request_info:145 - Headers: {'Accept': 'application/json', 'Content-Type': 'multipart/form-data; boundary=fa637535-ceeb-41e8-9a82-40b6b6bdd586', 'Content-Length': '459074', 'Host': 'localhost:9999', 'Connection': 'Keep-Alive', 'Accept-Encoding': 'gzip', 'User-Agent': 'okhttp/4.9.3'}
2025-07-24 10:01:11 - pp_structure_v3 - INFO - structure_file:297 - 处理上传文件: document.pdf
2025-07-24 10:01:11 - pp_structure_v3 - INFO - predict:102 - 开始使用PP-StructureV3处理 1 个图像文件
2025-07-24 10:01:11 - pp_structure_v3 - DEBUG - predict:106 - 处理第 1/1 个文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmp6zc2thyz.pdf
2025-07-24 10:01:11 - pp_structure_v3 - DEBUG - predict:110 - 文件扩展名: .pdf
2025-07-24 10:01:11 - pp_structure_v3 - INFO - predict:115 - 处理PDF文件: /var/folders/s6/2sf76nq53vb29b52_lpvjjd80000gn/T/tmp6zc2thyz.pdf
2025-07-24 10:07:21 - pp_structure_v3 - INFO - main:425 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 10:07:21 - pp_structure_v3 - INFO - main:426 - 服务地址: http://0.0.0.0:9999
2025-07-24 10:07:21 - pp_structure_v3 - INFO - main:427 - API文档:
2025-07-24 10:07:21 - pp_structure_v3 - INFO - main:428 -   GET  /              - 服务信息
2025-07-24 10:07:21 - pp_structure_v3 - INFO - main:429 -   GET  /health        - 健康检查
2025-07-24 10:07:21 - pp_structure_v3 - INFO - main:430 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 10:07:21 - pp_structure_v3 - INFO - init_model:80 - 开始初始化PP-StructureV3模型...
2025-07-24 10:07:21 - pp_structure_v3 - INFO - init_model:90 - 系统内存: 总计 32.0GB, 可用 17.6GB
2025-07-24 10:07:22 - pp_structure_v3 - INFO - init_model:101 - 使用自适应内存优化配置初始化模型...
2025-07-24 10:07:22 - pp_structure_v3 - ERROR - init_model:136 - PP-StructureV3初始化失败: Unknown argument: show_log
2025-07-24 10:07:22 - pp_structure_v3 - ERROR - log_error:158 - Error: Unknown argument: show_log
2025-07-24 10:07:22 - pp_structure_v3 - ERROR - log_error:160 - Context: PP-StructureV3初始化
2025-07-24 10:07:22 - pp_structure_v3 - ERROR - log_error:161 - Exception details:
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/PPStructureV3Server 2/pp_structure_v3_server.py", line 102, in init_model
    self.model = PPStructureV3(**model_config)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/pp_structurev3.py", line 98, in __init__
    super().__init__(**kwargs)
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/base.py", line 62, in __init__
    self._common_args = parse_common_args(
                        ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_common_args.py", line 43, in parse_common_args
    raise ValueError(f"Unknown argument: {name}")
ValueError: Unknown argument: show_log
2025-07-24 10:07:22 - pp_structure_v3 - WARNING - main:434 - 模型初始化失败，将在首次请求时重试
2025-07-24 10:07:22 - pp_structure_v3 - INFO - main:425 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 10:07:22 - pp_structure_v3 - INFO - main:426 - 服务地址: http://0.0.0.0:9999
2025-07-24 10:07:22 - pp_structure_v3 - INFO - main:427 - API文档:
2025-07-24 10:07:22 - pp_structure_v3 - INFO - main:428 -   GET  /              - 服务信息
2025-07-24 10:07:22 - pp_structure_v3 - INFO - main:429 -   GET  /health        - 健康检查
2025-07-24 10:07:22 - pp_structure_v3 - INFO - main:430 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 10:07:22 - pp_structure_v3 - INFO - init_model:80 - 开始初始化PP-StructureV3模型...
2025-07-24 10:07:22 - pp_structure_v3 - INFO - init_model:90 - 系统内存: 总计 32.0GB, 可用 17.5GB
2025-07-24 10:07:23 - pp_structure_v3 - INFO - init_model:101 - 使用自适应内存优化配置初始化模型...
2025-07-24 10:07:23 - pp_structure_v3 - ERROR - init_model:136 - PP-StructureV3初始化失败: Unknown argument: show_log
2025-07-24 10:07:23 - pp_structure_v3 - ERROR - log_error:158 - Error: Unknown argument: show_log
2025-07-24 10:07:23 - pp_structure_v3 - ERROR - log_error:160 - Context: PP-StructureV3初始化
2025-07-24 10:07:23 - pp_structure_v3 - ERROR - log_error:161 - Exception details:
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/PPStructureV3Server 2/pp_structure_v3_server.py", line 102, in init_model
    self.model = PPStructureV3(**model_config)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/pp_structurev3.py", line 98, in __init__
    super().__init__(**kwargs)
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/base.py", line 62, in __init__
    self._common_args = parse_common_args(
                        ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_common_args.py", line 43, in parse_common_args
    raise ValueError(f"Unknown argument: {name}")
ValueError: Unknown argument: show_log
2025-07-24 10:07:23 - pp_structure_v3 - WARNING - main:434 - 模型初始化失败，将在首次请求时重试
2025-07-24 10:08:12 - pp_structure_v3 - INFO - main:425 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 10:08:12 - pp_structure_v3 - INFO - main:426 - 服务地址: http://0.0.0.0:9999
2025-07-24 10:08:12 - pp_structure_v3 - INFO - main:427 - API文档:
2025-07-24 10:08:12 - pp_structure_v3 - INFO - main:428 -   GET  /              - 服务信息
2025-07-24 10:08:12 - pp_structure_v3 - INFO - main:429 -   GET  /health        - 健康检查
2025-07-24 10:08:12 - pp_structure_v3 - INFO - main:430 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 10:08:12 - pp_structure_v3 - INFO - init_model:80 - 开始初始化PP-StructureV3模型...
2025-07-24 10:08:12 - pp_structure_v3 - INFO - init_model:90 - 系统内存: 总计 32.0GB, 可用 17.4GB
2025-07-24 10:08:13 - pp_structure_v3 - INFO - init_model:101 - 使用自适应内存优化配置初始化模型...
2025-07-24 10:08:13 - pp_structure_v3 - ERROR - init_model:136 - PP-StructureV3初始化失败: Unknown argument: table_recognition_batch_size
2025-07-24 10:08:13 - pp_structure_v3 - ERROR - log_error:158 - Error: Unknown argument: table_recognition_batch_size
2025-07-24 10:08:13 - pp_structure_v3 - ERROR - log_error:160 - Context: PP-StructureV3初始化
2025-07-24 10:08:13 - pp_structure_v3 - ERROR - log_error:161 - Exception details:
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/PPStructureV3Server 2/pp_structure_v3_server.py", line 102, in init_model
    self.model = PPStructureV3(**model_config)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/pp_structurev3.py", line 98, in __init__
    super().__init__(**kwargs)
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/base.py", line 62, in __init__
    self._common_args = parse_common_args(
                        ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_common_args.py", line 43, in parse_common_args
    raise ValueError(f"Unknown argument: {name}")
ValueError: Unknown argument: table_recognition_batch_size
2025-07-24 10:08:13 - pp_structure_v3 - WARNING - main:434 - 模型初始化失败，将在首次请求时重试
2025-07-24 10:08:29 - pp_structure_v3 - INFO - main:418 - 启动PP-StructureV3文档结构化解析服务...
2025-07-24 10:08:29 - pp_structure_v3 - INFO - main:419 - 服务地址: http://0.0.0.0:9999
2025-07-24 10:08:29 - pp_structure_v3 - INFO - main:420 - API文档:
2025-07-24 10:08:29 - pp_structure_v3 - INFO - main:421 -   GET  /              - 服务信息
2025-07-24 10:08:29 - pp_structure_v3 - INFO - main:422 -   GET  /health        - 健康检查
2025-07-24 10:08:29 - pp_structure_v3 - INFO - main:423 -   POST /api/v1/structure/file  - 上传文件结构化解析
2025-07-24 10:08:29 - pp_structure_v3 - INFO - init_model:80 - 开始初始化PP-StructureV3模型...
2025-07-24 10:08:29 - pp_structure_v3 - INFO - init_model:90 - 系统内存: 总计 32.0GB, 可用 17.0GB
2025-07-24 10:08:30 - pp_structure_v3 - INFO - init_model:101 - 使用自适应内存优化配置初始化模型...
2025-07-24 10:08:30 - pp_structure_v3 - ERROR - init_model:129 - PP-StructureV3初始化失败: Unknown argument: table_recognition_batch_size
2025-07-24 10:08:30 - pp_structure_v3 - ERROR - log_error:158 - Error: Unknown argument: table_recognition_batch_size
2025-07-24 10:08:30 - pp_structure_v3 - ERROR - log_error:160 - Context: PP-StructureV3初始化
2025-07-24 10:08:30 - pp_structure_v3 - ERROR - log_error:161 - Exception details:
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/PPStructureV3Server 2/pp_structure_v3_server.py", line 102, in init_model
    self.model = PPStructureV3(**model_config)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/pp_structurev3.py", line 98, in __init__
    super().__init__(**kwargs)
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_pipelines/base.py", line 62, in __init__
    self._common_args = parse_common_args(
                        ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Downloads/paddleocr-pdf-table/venv/lib/python3.11/site-packages/paddleocr/_common_args.py", line 43, in parse_common_args
    raise ValueError(f"Unknown argument: {name}")
ValueError: Unknown argument: table_recognition_batch_size
2025-07-24 10:08:30 - pp_structure_v3 - WARNING - main:427 - 模型初始化失败，将在首次请求时重试
2025-07-24 10:10:42 - pp_structure_v3 - INFO - <module>:302 - 启动PP-StructureV3低内存模式服务...
2025-07-24 10:10:42 - pp_structure_v3 - INFO - <module>:303 - 服务地址: http://0.0.0.0:9999
2025-07-24 10:10:42 - pp_structure_v3 - INFO - <module>:304 - ⚠️  低内存模式已禁用部分功能以节省内存
2025-07-24 10:10:48 - pp_structure_v3 - INFO - log_request_info:144 - Request: GET http://127.0.0.1:9999/
2025-07-24 10:10:48 - pp_structure_v3 - INFO - log_response_info:152 - Response: 200
2025-07-24 10:10:53 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://127.0.0.1:9999/api/v1/structure/file
2025-07-24 10:10:53 - pp_structure_v3 - INFO - structure_file:263 - 处理上传文件: 1.pdf
2025-07-24 10:10:53 - pp_structure_v3 - INFO - init_model:76 - 开始初始化PP-StructureV3模型（低内存模式）...
2025-07-24 10:10:53 - pp_structure_v3 - INFO - init_model:80 - 系统内存: 总计 32.0GB, 可用 16.5GB (51.6%)
2025-07-24 10:10:57 - pp_structure_v3 - INFO - init_model:99 - 使用极简配置初始化模型...
2025-07-24 10:11:02 - pp_structure_v3 - INFO - init_model:116 - 模型加载后内存: 可用 14.7GB
2025-07-24 10:11:02 - pp_structure_v3 - INFO - init_model:117 - ✅ PP-StructureV3模型初始化成功（低内存模式）
2025-07-24 10:11:02 - pp_structure_v3 - INFO - predict:133 - 开始处理 1 个文件（低内存模式）
2025-07-24 10:11:02 - pp_structure_v3 - INFO - predict:137 - 处理第 1/1 个文件: tmpb15ej2ia.pdf
2025-07-24 10:11:20 - pp_structure_v3 - INFO - <module>:302 - 启动PP-StructureV3低内存模式服务...
2025-07-24 10:11:20 - pp_structure_v3 - INFO - <module>:303 - 服务地址: http://0.0.0.0:9999
2025-07-24 10:11:20 - pp_structure_v3 - INFO - <module>:304 - ⚠️  低内存模式已禁用部分功能以节省内存
2025-07-24 10:11:30 - pp_structure_v3 - INFO - log_request_info:144 - Request: POST http://127.0.0.1:9999/api/v1/structure/file
2025-07-24 10:11:30 - pp_structure_v3 - INFO - structure_file:263 - 处理上传文件: 1.pdf
2025-07-24 10:11:30 - pp_structure_v3 - INFO - init_model:76 - 开始初始化PP-StructureV3模型（低内存模式）...
2025-07-24 10:11:30 - pp_structure_v3 - INFO - init_model:80 - 系统内存: 总计 32.0GB, 可用 18.7GB (58.5%)
2025-07-24 10:11:31 - pp_structure_v3 - INFO - init_model:99 - 使用极简配置初始化模型...
2025-07-24 10:11:35 - pp_structure_v3 - INFO - init_model:116 - 模型加载后内存: 可用 17.0GB
2025-07-24 10:11:35 - pp_structure_v3 - INFO - init_model:117 - ✅ PP-StructureV3模型初始化成功（低内存模式）
2025-07-24 10:11:35 - pp_structure_v3 - INFO - predict:133 - 开始处理 1 个文件（低内存模式）
2025-07-24 10:11:35 - pp_structure_v3 - INFO - predict:137 - 处理第 1/1 个文件: tmp7fhbhn5a.pdf
