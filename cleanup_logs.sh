#!/bin/bash

# PP-StructureV3 日志清理脚本
# 用于清理指定天数之前的日志文件

# 默认保留天数
DEFAULT_KEEP_DAYS=1

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 显示帮助信息
show_help() {
    echo -e "${BLUE}PP-StructureV3 日志清理脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --days DAYS     保留最近DAYS天的日志 (默认: $DEFAULT_KEEP_DAYS)"
    echo "  -p, --path PATH     日志基础目录路径 (默认: logs)"
    echo "  -n, --dry-run       仅显示将要删除的文件，不实际删除"
    echo "  -f, --force         强制删除，不询问确认"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                  # 删除30天前的日志"
    echo "  $0 -d 7             # 删除7天前的日志"
    echo "  $0 -n               # 预览将要删除的日志"
    echo "  $0 -d 15 -f         # 强制删除15天前的日志"
    echo ""
}

# 解析命令行参数
KEEP_DAYS=$DEFAULT_KEEP_DAYS
LOG_BASE_DIR="logs"
DRY_RUN=false
FORCE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--days)
            KEEP_DAYS="$2"
            shift 2
            ;;
        -p|--path)
            LOG_BASE_DIR="$2"
            shift 2
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知参数: $1${NC}"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 验证参数
if ! [[ "$KEEP_DAYS" =~ ^[0-9]+$ ]] || [ "$KEEP_DAYS" -lt 1 ]; then
    echo -e "${RED}错误: 保留天数必须是正整数${NC}"
    exit 1
fi

if [ ! -d "$LOG_BASE_DIR" ]; then
    echo -e "${RED}错误: 日志目录不存在: $LOG_BASE_DIR${NC}"
    exit 1
fi

echo -e "${BLUE}=== PP-StructureV3 日志清理脚本 ===${NC}"
echo -e "${CYAN}日志目录: $LOG_BASE_DIR${NC}"
echo -e "${CYAN}保留天数: $KEEP_DAYS${NC}"
echo -e "${CYAN}预览模式: $DRY_RUN${NC}"
echo ""

# 查找要删除的日志目录
echo -e "${YELLOW}查找 $KEEP_DAYS 天前的日志目录...${NC}"

# 计算截止日期
CUTOFF_DATE=$(date -d "$KEEP_DAYS days ago" +%Y-%m-%d 2>/dev/null || date -v-${KEEP_DAYS}d +%Y-%m-%d 2>/dev/null)

if [ -z "$CUTOFF_DATE" ]; then
    echo -e "${RED}错误: 无法计算截止日期${NC}"
    exit 1
fi

echo -e "${CYAN}截止日期: $CUTOFF_DATE (保留此日期及之后的日志)${NC}"

# 查找所有日期目录并筛选
OLD_DIRS=""
for dir in "$LOG_BASE_DIR"/20*; do
    if [ -d "$dir" ]; then
        DIR_NAME=$(basename "$dir")
        # 检查目录名是否符合日期格式 YYYY-MM-DD
        if [[ "$DIR_NAME" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]]; then
            # 比较日期字符串（YYYY-MM-DD格式可以直接字符串比较）
            if [[ "$DIR_NAME" < "$CUTOFF_DATE" ]]; then
                if [ -z "$OLD_DIRS" ]; then
                    OLD_DIRS="$dir"
                else
                    OLD_DIRS="$OLD_DIRS"$'\n'"$dir"
                fi
            fi
        fi
    fi
done

if [ -z "$OLD_DIRS" ]; then
    echo -e "${GREEN}没有找到需要清理的日志目录${NC}"
    exit 0
fi

echo -e "${YELLOW}找到以下需要清理的日志目录:${NC}"
while IFS= read -r dir; do
    if [ -n "$dir" ]; then
        SIZE=$(du -sh "$dir" 2>/dev/null | cut -f1)
        echo -e "  ${RED}$dir${NC} (大小: $SIZE)"
    fi
done <<< "$OLD_DIRS"

# 计算总大小和目录数量
if [ -n "$OLD_DIRS" ]; then
    # 将目录列表转换为数组以便处理
    DIRS_ARRAY=()
    while IFS= read -r dir; do
        if [ -n "$dir" ]; then
            DIRS_ARRAY+=("$dir")
        fi
    done <<< "$OLD_DIRS"

    DIR_COUNT=${#DIRS_ARRAY[@]}

    # 计算总大小
    if [ ${#DIRS_ARRAY[@]} -gt 0 ]; then
        # 使用更简单的方法计算总大小
        TOTAL_SIZE_KB=0
        for dir in "${DIRS_ARRAY[@]}"; do
            if [ -d "$dir" ]; then
                DIR_SIZE=$(du -sk "$dir" 2>/dev/null | cut -f1)
                if [ -n "$DIR_SIZE" ] && [ "$DIR_SIZE" -gt 0 ]; then
                    TOTAL_SIZE_KB=$((TOTAL_SIZE_KB + DIR_SIZE))
                fi
            fi
        done

        # 转换为人类可读格式
        if [ $TOTAL_SIZE_KB -gt 1048576 ]; then
            TOTAL_SIZE="$((TOTAL_SIZE_KB / 1048576))GB"
        elif [ $TOTAL_SIZE_KB -gt 1024 ]; then
            TOTAL_SIZE="$((TOTAL_SIZE_KB / 1024))MB"
        else
            TOTAL_SIZE="${TOTAL_SIZE_KB}KB"
        fi
    else
        TOTAL_SIZE="0KB"
    fi
else
    DIR_COUNT=0
    TOTAL_SIZE="0"
fi

echo ""
echo -e "${CYAN}总计: $DIR_COUNT 个目录，总大小: $TOTAL_SIZE${NC}"

# 如果是预览模式，直接退出
if [ "$DRY_RUN" = true ]; then
    echo -e "${YELLOW}预览模式：以上目录将被删除（使用 --force 实际执行删除）${NC}"
    exit 0
fi

# 确认删除
if [ "$FORCE" != true ]; then
    echo ""
    echo -e "${YELLOW}确定要删除这些日志目录吗? (y/N)${NC}"
    read -r response
    if [[ ! "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        echo -e "${YELLOW}取消删除操作${NC}"
        exit 0
    fi
fi

# 执行删除
echo ""
echo -e "${YELLOW}开始删除日志目录...${NC}"

DELETED_COUNT=0
FAILED_COUNT=0

# 使用数组来避免管道中的变量作用域问题
while IFS= read -r dir; do
    if [ -n "$dir" ] && [ -d "$dir" ]; then
        echo -n "删除 $dir ... "
        if rm -rf "$dir" 2>/dev/null; then
            echo -e "${GREEN}成功${NC}"
            DELETED_COUNT=$((DELETED_COUNT + 1))
        else
            echo -e "${RED}失败${NC}"
            FAILED_COUNT=$((FAILED_COUNT + 1))
        fi
    fi
done <<< "$OLD_DIRS"

echo ""
if [ $FAILED_COUNT -eq 0 ]; then
    echo -e "${GREEN}✅ 日志清理完成！${NC}"
    echo -e "${GREEN}成功删除 $DELETED_COUNT 个日志目录${NC}"
else
    echo -e "${YELLOW}⚠ 日志清理完成，但有部分失败${NC}"
    echo -e "${GREEN}成功删除: $DELETED_COUNT 个目录${NC}"
    echo -e "${RED}删除失败: $FAILED_COUNT 个目录${NC}"
fi

# 显示清理后的状态
echo ""
echo -e "${CYAN}清理后的日志目录状态:${NC}"
if [ -d "$LOG_BASE_DIR" ]; then
    REMAINING_DIRS=$(find "$LOG_BASE_DIR" -maxdepth 1 -type d -name "20*" 2>/dev/null | wc -l)
    REMAINING_SIZE=$(du -sh "$LOG_BASE_DIR" 2>/dev/null | cut -f1)
    echo -e "${GREEN}剩余日志目录: $REMAINING_DIRS 个${NC}"
    echo -e "${GREEN}总大小: $REMAINING_SIZE${NC}"
    
    # 显示最近的几个日志目录
    echo -e "${CYAN}最近的日志目录:${NC}"
    RECENT_DIRS=$(find "$LOG_BASE_DIR" -maxdepth 1 -type d -name "20*" 2>/dev/null | sort -r | head -5)
    while IFS= read -r dir; do
        if [ -n "$dir" ]; then
            echo -e "  ${GREEN}$(basename "$dir")${NC}"
        fi
    done <<< "$RECENT_DIRS"
fi

echo ""
echo -e "${BLUE}日志清理脚本执行完成${NC}"
