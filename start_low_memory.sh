#!/bin/bash

# PP-StructureV3 极低内存模式启动脚本
# 适用于内存不足4GB的系统
# 使用方法: ./start_low_memory.sh [port]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 默认端口
DEFAULT_PORT=9999
PORT=${1:-$DEFAULT_PORT}

echo -e "${PURPLE}=== PP-StructureV3 极低内存模式启动脚本 ===${NC}"
echo -e "${RED}⚠️  警告：极低内存模式将禁用大部分功能${NC}"
echo -e "${YELLOW}只保留：文档版面分析、OCR文字识别、表格识别${NC}"
echo -e "${YELLOW}禁用：印章识别、公式识别、图表识别${NC}"
echo -e "${BLUE}服务端口: ${PORT}${NC}"
echo ""

# 检查系统内存
echo -e "${YELLOW}检查系统内存...${NC}"
if command -v free &> /dev/null; then
    TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.1f", $2/1024}')
    AVAIL_MEM=$(free -m | awk 'NR==2{printf "%.1f", $7/1024}')
    echo -e "${GREEN}总内存: ${TOTAL_MEM}GB, 可用内存: ${AVAIL_MEM}GB${NC}"
    
    if (( $(echo "$AVAIL_MEM < 2.0" | bc -l) )); then
        echo -e "${RED}⚠️  严重警告：可用内存不足2GB！${NC}"
        echo -e "${RED}建议：${NC}"
        echo -e "${RED}1. 关闭其他应用程序${NC}"
        echo -e "${RED}2. 重启系统释放内存${NC}"
        echo -e "${RED}3. 考虑增加系统内存${NC}"
        echo ""
        read -p "是否继续启动？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "启动已取消"
            exit 1
        fi
    elif (( $(echo "$AVAIL_MEM < 4.0" | bc -l) )); then
        echo -e "${YELLOW}⚠️  警告：可用内存不足4GB，性能可能受限${NC}"
    fi
elif command -v vm_stat &> /dev/null; then
    # macOS
    echo -e "${GREEN}macOS系统检测到${NC}"
    # 获取macOS内存信息
    VM_STAT=$(vm_stat)
    PAGE_SIZE=$(vm_stat | grep "page size" | awk '{print $8}')
    FREE_PAGES=$(vm_stat | grep "Pages free" | awk '{print $3}' | sed 's/\.//')
    INACTIVE_PAGES=$(vm_stat | grep "Pages inactive" | awk '{print $3}' | sed 's/\.//')
    
    if [ ! -z "$PAGE_SIZE" ] && [ ! -z "$FREE_PAGES" ]; then
        AVAIL_MEM_BYTES=$((($FREE_PAGES + $INACTIVE_PAGES) * $PAGE_SIZE))
        AVAIL_MEM_GB=$(echo "scale=1; $AVAIL_MEM_BYTES / 1024 / 1024 / 1024" | bc)
        echo -e "${GREEN}可用内存约: ${AVAIL_MEM_GB}GB${NC}"
    fi
fi

# 检查Python版本
echo -e "${YELLOW}检查Python环境...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}错误: 未找到Python3，请先安装Python3${NC}"
    exit 1
fi

PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
echo -e "${GREEN}Python版本: ${PYTHON_VERSION}${NC}"

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}创建虚拟环境...${NC}"
    python3 -m venv venv
    echo -e "${GREEN}虚拟环境创建完成${NC}"
fi

# 创建必要目录
echo -e "${YELLOW}创建必要目录...${NC}"
mkdir -p logs uploads

# 激活虚拟环境
echo -e "${YELLOW}激活虚拟环境...${NC}"
source venv/bin/activate

# 验证关键依赖
echo -e "${YELLOW}验证关键依赖...${NC}"
python3 -c "import flask; print('Flask: OK')" 2>/dev/null || echo -e "${RED}Flask未安装${NC}"
python3 -c "from paddleocr import PPStructureV3; print('PP-StructureV3: OK')" 2>/dev/null || echo -e "${YELLOW}PP-StructureV3未安装或有问题${NC}"

# 设置极严格的内存优化环境变量
echo -e "${YELLOW}设置极严格内存优化参数...${NC}"
export FLASK_APP=low_memory_server.py
export FLASK_ENV=production
export PADDLEX_AUTO_UPGRADE=False

# 线程限制
export OMP_NUM_THREADS=1
export MKL_NUM_THREADS=1
export NUMEXPR_NUM_THREADS=1
export OPENBLAS_NUM_THREADS=1

# PaddlePaddle极严格内存限制
export FLAGS_fraction_of_gpu_memory_to_use=0.01
export FLAGS_allocator_strategy=naive_best_fit
export FLAGS_eager_delete_tensor_gb=0.0
export FLAGS_memory_fraction_of_eager_deletion=1.0

# Python内存优化
export PYTHONHASHSEED=0
export MALLOC_TRIM_THRESHOLD_=50000
export PYTHONOPTIMIZE=1  # 启用Python优化

# 限制Python内存使用
ulimit -v 4194304 2>/dev/null || echo -e "${YELLOW}无法设置内存限制${NC}"

# 清理系统缓存（如果可能）
if command -v sync &> /dev/null; then
    sync
fi

echo ""
echo -e "${PURPLE}=== 启动PP-StructureV3极低内存模式服务 ===${NC}"
echo -e "${GREEN}服务地址: http://localhost:${PORT}${NC}"
echo -e "${GREEN}健康检查: http://localhost:${PORT}/health${NC}"
echo ""
echo -e "${YELLOW}功能说明：${NC}"
echo -e "${GREEN}✅ 文档版面分析${NC}"
echo -e "${GREEN}✅ OCR文字识别${NC}"
echo -e "${GREEN}✅ 表格结构识别${NC}"
echo -e "${RED}❌ 印章识别（已禁用）${NC}"
echo -e "${RED}❌ 公式识别（已禁用）${NC}"
echo -e "${RED}❌ 图表识别（已禁用）${NC}"
echo ""
echo -e "${YELLOW}内存优化措施：${NC}"
echo -e "${YELLOW}• 限制图像处理尺寸${NC}"
echo -e "${YELLOW}• 禁用GPU使用${NC}"
echo -e "${YELLOW}• 单线程处理${NC}"
echo -e "${YELLOW}• 积极垃圾回收${NC}"
echo ""
echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
echo ""

# 启动低内存模式服务
python3 low_memory_server.py --port ${PORT} --host 0.0.0.0
