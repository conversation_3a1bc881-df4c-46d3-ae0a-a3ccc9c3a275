#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PP-StructureV3 低内存模式服务器
专门针对内存不足的环境优化
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import tempfile
import traceback
import time
import gc
import psutil
from functools import wraps
from werkzeug.utils import secure_filename

# 导入配置和日志模块
from config import config, Config
from logger import setup_logger, log_request_info, log_response_info, log_error

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 启用CORS
    CORS(app)
    
    return app

# 创建应用实例
app = create_app()

# 设置日志
logger = setup_logger('pp_structure_v3', app.config.get('LOG_LEVEL', 'INFO'))

def log_api_call(f):
    """API调用日志装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        log_request_info(logger, request)
        try:
            result = f(*args, **kwargs)
            log_response_info(logger, result)
            return result
        except Exception as e:
            log_error(logger, e, f"API调用失败: {f.__name__}")
            raise
    return decorated_function

class LowMemoryPPStructureV3Service:
    """低内存模式的PP-StructureV3服务"""
    
    def __init__(self):
        self.model = None
        self.model_type = None
        self.is_initialized = False
        
    def get_memory_info(self):
        """获取内存信息"""
        memory = psutil.virtual_memory()
        return {
            'total_gb': memory.total / (1024**3),
            'available_gb': memory.available / (1024**3),
            'percent': memory.percent
        }
        
    def init_model(self):
        """初始化PP-StructureV3模型（低内存模式）"""
        if self.is_initialized:
            return True

        try:
            logger.info("开始初始化PP-StructureV3模型（低内存模式）...")
            
            # 检查内存
            memory_info = self.get_memory_info()
            logger.info(f"系统内存: 总计 {memory_info['total_gb']:.1f}GB, "
                       f"可用 {memory_info['available_gb']:.1f}GB ({100-memory_info['percent']:.1f}%)")
            
            if memory_info['available_gb'] < 2.0:
                logger.warning("可用内存不足2GB，强烈建议释放更多内存")
                
            # 设置环境变量优化内存
            os.environ['OMP_NUM_THREADS'] = '1'
            os.environ['MKL_NUM_THREADS'] = '1'
            os.environ['NUMEXPR_NUM_THREADS'] = '1'
            os.environ['OPENBLAS_NUM_THREADS'] = '1'
            
            # 强制垃圾回收
            gc.collect()

            # 导入PP-StructureV3
            from paddleocr import PPStructureV3
            
            # 极简配置 - 只保留最基本的功能
            logger.info("使用极简配置初始化模型...")
            self.model = PPStructureV3(
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False,
                use_seal_recognition=False,
                use_chart_recognition=False,
                use_formula_recognition=False,
                use_table_recognition=True,  # 只保留表格识别
                text_det_limit_side_len=640,  # 限制图像尺寸
            )
            
            self.model_type = "ppstructurev3_low_memory"
            self.is_initialized = True
            
            # 再次检查内存
            memory_info_after = self.get_memory_info()
            logger.info(f"模型加载后内存: 可用 {memory_info_after['available_gb']:.1f}GB")
            logger.info("✅ PP-StructureV3模型初始化成功（低内存模式）")
            
            return True

        except Exception as e:
            logger.error(f"PP-StructureV3初始化失败: {e}")
            log_error(logger, e, "PP-StructureV3初始化")
            return False
    
    def predict(self, image_paths):
        """PP-StructureV3预测（低内存模式）"""
        if not self.is_initialized:
            if not self.init_model():
                raise Exception("PP-StructureV3模型初始化失败")

        try:
            logger.info(f"开始处理 {len(image_paths)} 个文件（低内存模式）")
            results = []

            for i, image_path in enumerate(image_paths):
                logger.info(f"处理第 {i+1}/{len(image_paths)} 个文件: {os.path.basename(image_path)}")
                
                # 检查内存压力
                memory_info = self.get_memory_info()
                if memory_info['percent'] > 90:
                    logger.warning(f"内存使用率过高: {memory_info['percent']:.1f}%")
                    gc.collect()  # 强制垃圾回收
                
                # 检查文件类型和大小
                file_ext = os.path.splitext(image_path)[1].lower()
                file_size = os.path.getsize(image_path) / (1024 * 1024)  # MB
                
                if file_ext == '.pdf' and file_size > 20:
                    logger.warning(f"PDF文件较大({file_size:.1f}MB)，可能导致内存不足")
                
                # 处理前垃圾回收
                gc.collect()
                
                try:
                    start_time = time.time()
                    output = self.model.predict(image_path)
                    end_time = time.time()
                    
                    logger.info(f"处理完成，耗时: {end_time - start_time:.1f}秒")
                    
                    # 处理后垃圾回收
                    gc.collect()
                    
                    # 简化结果处理
                    if output and len(output) > 0:
                        for idx, res in enumerate(output):
                            result_dict = {
                                "image_path": image_path,
                                "page_number": idx + 1 if file_ext == '.pdf' else 1,
                                "model_type": self.model_type,
                                "file_type": file_ext,
                                "processing_time": end_time - start_time
                            }
                            
                            # 尝试提取基本信息
                            try:
                                if hasattr(res, 'to_dict'):
                                    result_dict.update(res.to_dict())
                                else:
                                    result_dict["raw_result"] = str(res)
                            except Exception as e:
                                logger.warning(f"结果转换失败: {e}")
                                result_dict["raw_result"] = str(res)
                            
                            results.append(result_dict)
                    
                except Exception as e:
                    logger.error(f"文件处理失败: {e}")
                    # 继续处理下一个文件，不中断整个流程
                    results.append({
                        "image_path": image_path,
                        "error": str(e),
                        "model_type": self.model_type
                    })

            logger.info(f"处理完成，共生成 {len(results)} 个结果")
            return results

        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            raise Exception(f"PP-StructureV3预测失败: {e}")

# 创建服务实例
structure_service = LowMemoryPPStructureV3Service()

@app.route('/')
@log_api_call
def index():
    """服务信息"""
    memory_info = structure_service.get_memory_info()
    return jsonify({
        "message": "PP-StructureV3 Low Memory Mode Service",
        "description": "低内存模式的文档结构化解析服务",
        "version": "1.0.0-low-memory",
        "model": "PP-StructureV3",
        "mode": "low_memory",
        "model_status": "initialized" if structure_service.is_initialized else "not_initialized",
        "memory_info": memory_info,
        "features": [
            "文档版面分析",
            "OCR文字识别", 
            "表格结构识别"
        ],
        "disabled_features": [
            "印章识别",
            "公式识别",
            "图表识别"
        ]
    })

@app.route('/health')
@log_api_call
def health():
    """健康检查"""
    memory_info = structure_service.get_memory_info()
    return jsonify({
        "status": "healthy",
        "model_initialized": structure_service.is_initialized,
        "memory_usage": memory_info
    })

@app.route('/api/v1/structure/file', methods=['POST'])
@log_api_call
def structure_file():
    """文件上传结构化解析"""
    if 'file' not in request.files:
        return jsonify({
            "status": "error",
            "message": "没有上传文件",
            "code": "NO_FILE"
        }), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({
            "status": "error", 
            "message": "文件名为空",
            "code": "EMPTY_FILENAME"
        }), 400
        
    filename = secure_filename(file.filename)
    logger.info(f"处理上传文件: {filename}")
    
    temp_path = None
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as tmp_file:
            file.save(tmp_file.name)
            temp_path = tmp_file.name
        
        results = structure_service.predict([temp_path])
        
        logger.info(f"文件 {filename} 解析完成")
        return jsonify({
            "status": "success",
            "message": "文档结构化解析完成",
            "filename": filename,
            "results": results
        })
        
    except Exception as e:
        logger.error(f"文件解析失败: {filename}, 错误: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"结构化解析失败: {str(e)}",
            "code": "PROCESSING_ERROR"
        }), 500
    finally:
        if temp_path and os.path.exists(temp_path):
            os.unlink(temp_path)

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='PP-StructureV3 低内存模式服务')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=9999, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    args = parser.parse_args()
    
    logger.info("启动PP-StructureV3低内存模式服务...")
    logger.info(f"服务地址: http://{args.host}:{args.port}")
    logger.info("⚠️  低内存模式已禁用部分功能以节省内存")
    
    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug
    )
