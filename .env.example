# PPStructureV3服务配置示例
# 复制此文件为 .env 并根据需要修改配置

# ==================== 服务配置 ====================
# 服务器主机地址
HOST=0.0.0.0

# 服务器端口
PORT=9999

# 调试模式 (True/False)
DEBUG=True

# Flask配置环境 (development/production/testing)
FLASK_CONFIG=development

# ==================== 文件上传配置 ====================
# 最大文件大小 (字节) - 16MB
MAX_CONTENT_LENGTH=16777216

# 上传文件存储目录
UPLOAD_FOLDER=uploads

# ==================== OCR模型配置 ====================
# 是否使用角度分类器
USE_ANGLE_CLS=True

# OCR识别语言 (ch/en/korean/japan等)
OCR_LANG=ch

# 是否使用GPU加速 (True/False)
USE_GPU=False

# PaddleX自动升级设置
PADDLEX_AUTO_UPGRADE=False

# ==================== 日志配置 ====================
# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# 日志基础目录 (每天会在此目录下创建日期子目录)
LOG_BASE_DIR=logs

# 日志文件最大大小 (字节) - 10MB (保留配置，暂未使用)
LOG_MAX_BYTES=10485760

# 日志文件备份数量 (保留配置，暂未使用)
LOG_BACKUP_COUNT=5

# ==================== 性能配置 ====================
# 是否启用缓存
ENABLE_CACHE=False

# 缓存过期时间 (秒)
CACHE_TTL=3600

# ==================== 安全配置 ====================
# Flask密钥 (生产环境请使用随机字符串)
SECRET_KEY=your-secret-key-change-this-in-production

# 是否启用CORS跨域支持
ENABLE_CORS=True
